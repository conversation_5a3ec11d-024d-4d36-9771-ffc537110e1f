name: Tests

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:  # Allows manual triggering

# Limit concurrent runs of this workflow
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest]
        python-version: ['3.9', '3.10', '3.11', '3.12', '3.13']
        exclude:
          # Exclude problematic combinations where mappy fails to build
          - os: macos-latest
            python-version: '3.12'
          - os: macos-latest
            python-version: '3.13'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'

    - name: Install system dependencies
      run: |
        if [[ "$RUNNER_OS" == "Linux" ]]; then
          sudo apt-get update
          sudo apt-get install -y build-essential zlib1g-dev
        fi

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        python -m pip install pytest pytest-cov
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        python -m pip install -e .

    - name: Run unit tests
      run: |
        pytest tests/unit/ -v --cov=funannotate2 --cov-report=xml

    - name: Run integration tests without external dependencies
      run: |
        pytest tests/integration/test_funannotate_cli.py -v

    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v5
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
        slug: nextgenusfs/funannotate2
